import os
from openpyxl import load_workbook, Workbook
from openpyxl.drawing.image import Image as XLImage
from openpyxl.utils import get_column_letter
from PIL import Image
import io

# === CONFIG ===
input_excel = "14K TOE RINGS.xlsx"  # Your source Excel file
output_folder = "14K TOE RINGS"
output_excel = "14K TOE RINGS GRAMS.xlsx"
start_cell = "A3"
end_cell = "E42"

# === CREATE OUTPUT FOLDER ===
os.makedirs(output_folder, exist_ok=True)

# === LOAD WORKBOOK ===
wb = load_workbook(input_excel)
ws = wb.active

# === IMAGE MAPPING ===
image_map = {}
for image in ws._images:
    anchor = image.anchor._from
    col_letter = get_column_letter(anchor.col + 1)
    cell = f"{col_letter}{anchor.row + 1}"
    image_map[cell] = image

# === PARSE IMAGE GRID ===
from openpyxl.utils import column_index_from_string, get_column_letter

start_col = column_index_from_string(start_cell[0])
end_col = column_index_from_string(end_cell[0])
start_row = int(start_cell[1:])
end_row = int(end_cell[1:])

# === COLLECT DATA ===
style_gram_list = []

for col in range(start_col, end_col + 1):
    col_letter = get_column_letter(col)
    row = start_row
    while row <= end_row:
        image_cell = f"{col_letter}{row}"
        style_cell = f"{col_letter}{row+1}"
        gram_cell = f"{col_letter}{row+2}"

        style_no = ws[style_cell].value
        gram = ws[gram_cell].value

        if image_cell in image_map and style_no:
            style_no = style_no.strip()  # <- Strip it once here

            img = image_map[image_cell]._data()
            image = Image.open(io.BytesIO(img))
            name = f"{style_no}.png".replace("/", "_").replace(" ", "_")
            image.save(os.path.join(output_folder, name))

            style_gram_list.append((style_no, gram))


        row += 3  # Move to the next set in this column

# === WRITE TO NEW EXCEL ===
wb_out = Workbook()
ws_out = wb_out.active
ws_out.title = "Style and Gram"
ws_out.append(["Style Number", "Gram"])

for style, gram in style_gram_list:
    ws_out.append([style, gram])

wb_out.save(output_excel)

print(f"✅ Done. Saved {len(style_gram_list)} images and created '{output_excel}'")
