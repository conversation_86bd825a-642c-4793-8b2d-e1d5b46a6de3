import os
import re
from openpyxl import load_workbook, Workbook
from openpyxl.utils import get_column_letter
from PIL import Image
import io

def clean_style_number(style_no):
    """
    Clean style number by removing anything in parentheses
    Example: CAI048-14K (MQ8*4M) -> CAI048-14K
    """
    if not style_no:
        return ""

    # Remove anything in parentheses including the parentheses
    cleaned = re.sub(r'\s*\([^)]*\)', '', str(style_no))
    return cleaned.strip()

def find_style_and_gram_for_image(ws, image_cell, max_search_rows=10):
    """
    Search for style number and gram in cells below the image cell
    Returns tuple (style_number, gram) - both can be None if not found
    """
    col_letter = ''.join(filter(str.isalpha, image_cell))
    row_number = int(''.join(filter(str.isdigit, image_cell)))

    found_values = []

    # Search in cells below the image
    for offset in range(1, max_search_rows + 1):
        search_cell = f"{col_letter}{row_number + offset}"
        cell_value = ws[search_cell].value
        if cell_value and str(cell_value).strip():
            found_values.append(str(cell_value).strip())

    # Try to identify style number and gram from found values
    style_number = None
    gram = None

    for value in found_values:
        # Check if value looks like a style number (contains letters and numbers)
        if re.search(r'[A-Za-z].*\d|[A-Za-z].*[A-Za-z]', value) and not style_number:
            style_number = value
        # Check if value looks like gram (contains 'g' or 'gram' or is just a number)
        elif (re.search(r'\d+\s*g(?:ram)?s?$', value.lower()) or
              (value.replace('.', '').isdigit() and not style_number)) and not gram:
            gram = value

    return style_number, gram

# === CONFIGURATION ===
input_excel = "CARTILAGES.xlsx"  # Input Excel file
output_folder = "CARTILAGES 14K extracted_images"      # Output folder for images
output_excel = "CARTILAGES 14K style_data.xlsx"        # Output Excel with style data

# === SETUP OUTPUT FOLDER ===
os.makedirs(output_folder, exist_ok=True)

# === LOAD THE WORKBOOK ===
print("Loading Excel file...")
wb = load_workbook(input_excel)
ws = wb.active

# === MAP IMAGES TO ANCHOR CELLS ===
print("Mapping images to cells...")
image_map = {}
for image in ws._images:
    # Get cell anchor for each image
    anchor = image.anchor._from
    col_letter = get_column_letter(anchor.col + 1)
    row_number = anchor.row + 1
    cell = f"{col_letter}{row_number}"
    image_map[cell] = image

print(f"Found {len(image_map)} images in the Excel file")

# === PROCESS IMAGES AND EXTRACT STYLE NUMBERS ===
processed_data = []
saved_count = 0

for image_cell, image_obj in image_map.items():
    print(f"Processing image in cell {image_cell}...")

    # Find style number and gram in cells below the image
    raw_style_no, raw_gram = find_style_and_gram_for_image(ws, image_cell)

    if raw_style_no:
        # Clean the style number
        cleaned_style_no = clean_style_number(raw_style_no)

        if cleaned_style_no:
            # Determine color based on style number
            if "WG" in cleaned_style_no:
                color = "White"
            elif "PG" in cleaned_style_no:
                color = "Pink"
            else:
                color = "Yellow"

            # Print style number, color, and gram
            print(f"  Style Number: {cleaned_style_no}")
            print(f"  Color: {color}")
            print(f"  Gram: {raw_gram if raw_gram else 'Not found'}")

            try:
                # Save image with cleaned style number as filename
                img_bytes = image_obj._data()
                image = Image.open(io.BytesIO(img_bytes))

                # Create safe filename
                safe_filename = re.sub(r'[<>:"/\\|?*]', '_', cleaned_style_no)
                image_path = os.path.join(output_folder, f"{safe_filename}.png")

                image.save(image_path)
                print(f"  Saved: {image_path}")

                # Store data for Excel output
                processed_data.append({
                    'cell': image_cell,
                    'raw_style': raw_style_no,
                    'cleaned_style': cleaned_style_no,
                    'color': color,
                    'gram': raw_gram,
                    'filename': f"{safe_filename}.png"
                })

                saved_count += 1

            except Exception as e:
                print(f"  Error saving image: {e}")
        else:
            print(f"  No valid style number found after cleaning: {raw_style_no}")
    else:
        print(f"  No style number found below image in {image_cell}")

# === WRITE OUTPUT EXCEL ===
print("\nCreating output Excel file...")
wb_out = Workbook()
ws_out = wb_out.active
ws_out.title = "Extracted Images Data"

# Add headers
headers = ["Image Cell", "Raw Style Number", "Cleaned Style Number", "Color", "Gram", "Filename"]
ws_out.append(headers)

# Add data
for data in processed_data:
    ws_out.append([
        data['cell'],
        data['raw_style'],
        data['cleaned_style'],
        data['color'],
        data['gram'],
        data['filename']
    ])

wb_out.save(output_excel)

print(f"\n✅ Processing complete!")
print(f"   - Processed {len(image_map)} images")
print(f"   - Successfully saved {saved_count} images to '{output_folder}' folder")
print(f"   - Created data file: '{output_excel}'")
print(f"   - Images saved as PNG with cleaned style numbers as filenames")
